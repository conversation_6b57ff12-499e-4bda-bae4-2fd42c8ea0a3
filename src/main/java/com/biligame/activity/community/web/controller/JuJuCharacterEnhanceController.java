package com.biligame.activity.community.web.controller;

import com.biligame.activity.community.application.query.JuJuCharacterEnhanceQueryService;
import com.biligame.activity.community.application.query.dto.character.*;
import com.biligame.activity.community.infrastructure.annotation.LoginLimit;
import com.biligame.activity.community.web.vo.DataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/26 16:48
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/juju/character")
public class JuJuCharacterEnhanceController {
    
    @Resource
    private JuJuCharacterEnhanceQueryService juJuCharacterEnhanceQueryService;
    
    @GetMapping("/leaderboard")
    @LoginLimit(type = {LoginLimit.OauthType.TOKEN, LoginLimit.OauthType.COOKIE}, needCpUid = true)
    public DataResponse<JuJuCharacterEnhanceDTO> getUserCharacterEnhanceInfo(@RequestParam("game_id") Integer gameId,
                                                                             @RequestParam("app_id") Integer appId,
                                                                             @RequestParam("lang") String lang,
                                                                             @RequestParam("card_id") String cardId) {
        UserCharacterEnhanceQuery query = UserCharacterEnhanceQuery.builder()
                .appId(appId)
                .gameId(gameId)
                .lang(lang)
                .cardId(cardId)
                .build();
        JuJuCharacterEnhanceDTO data = juJuCharacterEnhanceQueryService.getUserCharacterEnhanceInfo(query);
        return new DataResponse<>(data);
    }
    
    /**
     * 获取角色卡牌列表
     * @param gameId
     * @param appId
     * @param lang
     * @return
     */
    @GetMapping("/list")
    @LoginLimit(type = {LoginLimit.OauthType.TOKEN, LoginLimit.OauthType.COOKIE}, needCpUid = true)
    public DataResponse<List<JuJuCharacterCardDTO>> getCharacterList(@RequestParam("game_id") Integer gameId,
                                                                     @RequestParam("app_id") Integer appId,
                                                                     @RequestParam("lang") String lang) {
        
        UserCharacterEnhanceQuery query = UserCharacterEnhanceQuery.builder()
                .appId(appId)
                .gameId(gameId)
                .lang(lang)
                .build();
        List<JuJuCharacterCardDTO> data = juJuCharacterEnhanceQueryService.getCharacterCardList(query);
        return new DataResponse<>(data);
    }
    
    /**
     * 获取用户奖励列表
     * @param gameId 游戏ID
     * @param appId dip 区服ID
     * @param lang 语言
     * @param cardId 卡牌ID
     * @return 用户奖励列表
     */
    @GetMapping("/rewards")
    public DataResponse<JuJuUserCharacterRewardDTO> getUserRewardList(@RequestParam("game_id") Integer gameId,
                                                                      @RequestParam("app_id") Integer appId,
                                                                      @RequestParam("lang") String lang,
                                                                      @RequestParam("card_id") String cardId) {
        UserCharacterEnhanceQuery query = UserCharacterEnhanceQuery.builder()
                .appId(appId)
                .gameId(gameId)
                .lang(lang)
                .cardId(cardId)
                .build();
        JuJuUserCharacterRewardDTO data = juJuCharacterEnhanceQueryService.getUserRewardList(query);
        return new DataResponse<>(data);
    }
}
