package com.biligame.activity.community.application.query.dto.character;

import com.biligame.activity.community.application.query.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2025/5/28 11:50
 * @description
 */
@Getter
@Setter
public class UserCharacterEnhanceQuery extends BaseDTO {
    private static final long serialVersionUID = 3580769064081570480L;
    
    private Integer appId;
    
    private String lang;
    
    private String cardId;
    
    private String gameId;
}
