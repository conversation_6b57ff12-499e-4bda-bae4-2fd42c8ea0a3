package com.biligame.activity.community.application.query.repository;

import com.biligame.activity.community.application.query.repository.dataobject.JuJuCharacterEnhanceInfoDO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuPrizeGrantRecordDO;
import com.biligame.activity.community.application.query.repository.dataobject.SupportCardInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/27 19:57
 * @description
 */
public interface JuJuCharacterEnhanceQueryRepository {

    // 已开放的卡牌信息列表
    List<SupportCardInfoDO> getOpenedCardList();
    
    // 已领取的奖励列表
    List<JuJuPrizeGrantRecordDO> getReceivedRewardList(String roleId);
    
    JuJuCharacterEnhanceInfoDO getUserCharacterEnhanceInfo(String roleId, String cardId, String ds);
    
    List<JuJuCharacterEnhanceInfoDO> getCharacterEnhanceLeaderboard(String cardId, String server, Integer limit, String ds);
    
    List<JuJuCharacterEnhanceInfoDO> getGlobalCharacterEnhanceLeaderboard(String cardId, Integer limit, String ds);
}
