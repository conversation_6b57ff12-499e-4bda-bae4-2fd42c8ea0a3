package com.biligame.activity.community.application.query.dto.character;

import com.biligame.activity.community.application.query.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/28 11:49
 * @description
 */
@Getter
@Setter
public class JuJuCharacterEnhanceDTO extends BaseDTO {
    private static final long serialVersionUID = 1046842558192941932L;
    
    /**
     * 用户角色强化信息
     */
    private UserCharacterEnhanceInfo userCharacterEnhanceInfo;
    
    /**
     * 本服角色强化排行榜
     */
    private List<UserCharacterEnhanceInfo> characterEnhanceLeaderboard;
    
    /**
     * 全服角色强化排行榜
     */
    private List<UserCharacterEnhanceInfo> globalCharacterEnhanceLeaderboard;
    
    
    @Getter
    @Setter
    public static class UserCharacterEnhanceInfo extends BaseDTO {
        private static final long serialVersionUID = 1046842558192941932L;
        
        private String cardId;
        
        private String cpUid;
        
        private String roleId;
        
        private String roleName;
        
        private Integer combatScore;
        
        private String combatTier;
        
        private Integer rank;
    }
    
}
