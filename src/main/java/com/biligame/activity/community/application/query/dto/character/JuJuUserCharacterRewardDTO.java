package com.biligame.activity.community.application.query.dto.character;

import com.biligame.activity.community.application.query.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/28 15:43
 * @description
 */
@Getter
@Setter
public class JuJuUserCharacterRewardDTO extends BaseDTO {
    private static final long serialVersionUID = 3244579705252847264L;
    
    private String cardId;
    
    private String currentCombatTier;
    
    private Integer combatScore;
    
    private List<JuJuUserAwardDTO> awards;
}
