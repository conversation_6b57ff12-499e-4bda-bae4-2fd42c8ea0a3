package com.biligame.activity.community.application.query.repository.dataobject;

import com.biligame.activity.community.infrastructure.enums.JuJuServerEnum;
import com.biligame.activity.community.shared.dataobject.BaseDO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2025/5/28 11:08
 * @description
 */
@Getter
@Setter
public class JuJuCharacterEnhanceInfoDO extends BaseDO {
    private static final long serialVersionUID = -5898546033597714505L;
    
    private String cardId;
    
    private String cpUid;
    
    private String roleId;
    
    private String roleName;
    
    private Integer combatScore;
    
    private String combatTier;
    
    private Integer rank;
    
    private Integer globalRank;
    
    private JuJuServerEnum server;
}
