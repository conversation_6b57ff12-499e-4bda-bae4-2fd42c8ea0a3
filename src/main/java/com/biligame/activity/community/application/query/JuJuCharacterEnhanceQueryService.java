package com.biligame.activity.community.application.query;

import com.biligame.activity.community.application.query.dto.character.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/26 16:47
 * @description
 */
public interface JuJuCharacterEnhanceQueryService {
    
    /**
     * 获取玩家角色强化信息
     * @param query
     * @return
     */
    JuJuCharacterEnhanceDTO getUserCharacterEnhanceInfo(UserCharacterEnhanceQuery query);
    
    /**
     * 获取角色卡牌信息
     * @return
     */
    List<JuJuCharacterCardDTO> getCharacterCardList(UserCharacterEnhanceQuery query);
    
    JuJuUserCharacterRewardDTO getUserRewardList(UserCharacterEnhanceQuery query);
}
