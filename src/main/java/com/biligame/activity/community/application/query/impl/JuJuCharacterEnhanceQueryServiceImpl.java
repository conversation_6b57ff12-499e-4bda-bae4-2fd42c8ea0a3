package com.biligame.activity.community.application.query.impl;

import com.biligame.activity.community.application.command.config.JuJuCharacterEnhanceConfig;
import com.biligame.activity.community.application.query.JuJuCharacterEnhanceQueryService;
import com.biligame.activity.community.application.query.assembler.JuJuCharacterEnhanceAssembler;
import com.biligame.activity.community.application.query.config.JuJuAchievementConfig;
import com.biligame.activity.community.application.query.dto.character.JuJuCharacterCardDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuCharacterEnhanceDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuUserCharacterRewardDTO;
import com.biligame.activity.community.application.query.dto.character.UserCharacterEnhanceQuery;
import com.biligame.activity.community.application.query.repository.JuJuCharacterEnhanceQueryRepository;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuCharacterEnhanceInfoDO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuPrizeGrantRecordDO;
import com.biligame.activity.community.application.query.repository.dataobject.SupportCardInfoDO;
import com.biligame.activity.community.infrastructure.enums.JuJuServerEnum;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.JuJuCharacterCardInfoDO;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.JuJuRoleBaseInfoDO;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.RoleInfoDO;
import com.biligame.activity.community.infrastructure.rpc.dip.service.GameBDIPService;
import com.biligame.activity.community.infrastructure.rpc.meta.dataobject.GameSDKInfoDO;
import com.biligame.activity.community.infrastructure.rpc.meta.service.GameSDKInfoService;
import com.biligame.activity.community.shared.entity.AccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.context.Context;
import pleiades.venus.context.User;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/5/27 19:56
 * @description
 */
@Slf4j
@Service
public class JuJuCharacterEnhanceQueryServiceImpl implements JuJuCharacterEnhanceQueryService {
    
    @Resource
    private JuJuCharacterEnhanceAssembler assembler;
    
    @Resource
    private GameBDIPService gameBDIPService;
    
    @Resource
    private JuJuCharacterEnhanceQueryRepository juJuCharacterEnhanceQueryRepository;
    
    @Resource
    private GameSDKInfoService gameSDKInfoService;
    
    @Resource
    private JuJuAchievementConfig juJuAchievementConfig;
    
    @Resource
    private ThreadPoolTaskExecutor customThreadPoolTaskExecutor;
    
    @Resource
    private JuJuCharacterEnhanceConfig juJuCharacterEnhanceConfig;
    
    private static final Integer DEFAULT_LIMIT_FOR_CHARACTER_ENHANCE = 100;
    
    @Override
    public JuJuCharacterEnhanceDTO getUserCharacterEnhanceInfo(UserCharacterEnhanceQuery query) {
        User user = Context.current().getUser();
        Long cpUid = user != null ?  ((AccountInfo) user).getCpUid() : null;
        RoleInfoDO roleInfo = null;
        JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo = null;

        JuJuServerEnum juJuServerEnum = JuJuServerEnum.getByAppId(query.getAppId());
        List<JuJuCharacterEnhanceInfoDO> enhanceLeaderboard = juJuCharacterEnhanceQueryRepository.getCharacterEnhanceLeaderboard(query.getCardId(), juJuServerEnum.getCpServer(), DEFAULT_LIMIT_FOR_CHARACTER_ENHANCE, juJuAchievementConfig.getDayVal());
        List<JuJuCharacterEnhanceInfoDO> globalCharacterEnhanceLeaderboard = juJuCharacterEnhanceQueryRepository.getGlobalCharacterEnhanceLeaderboard(query.getCardId(), DEFAULT_LIMIT_FOR_CHARACTER_ENHANCE, juJuAchievementConfig.getDayVal());
        if( cpUid != null) {
            userCharacterEnhanceInfo = juJuCharacterEnhanceQueryRepository.getUserCharacterEnhanceInfoByCpUid(String.valueOf(cpUid), query.getCardId(), juJuAchievementConfig.getDayVal());
        }
        if (userCharacterEnhanceInfo == null) {
            roleInfo = getRoleInfo(user, query.getAppId());
            if (roleInfo != null) {
                userCharacterEnhanceInfo = juJuCharacterEnhanceQueryRepository.getUserCharacterEnhanceInfoByRoleId(roleInfo.getRoleId(), query.getCardId(), juJuAchievementConfig.getDayVal());
            }
            log.info("getUserCharacterEnhanceInfo user character info by role_id, cpUid:{}, roleInfo:{}", cpUid, roleInfo);
        }
        
        
        updateCharacterEnhanceRoleName(enhanceLeaderboard);
        updateCharacterEnhanceRoleName(globalCharacterEnhanceLeaderboard);
        
        return assembler.toJuJuCharacterEnhanceDTO(userCharacterEnhanceInfo, enhanceLeaderboard, globalCharacterEnhanceLeaderboard);
    }
    
    @Override
    public List<JuJuCharacterCardDTO> getCharacterCardList(UserCharacterEnhanceQuery query) {
        User user = Context.current().getUser();
        RoleInfoDO roleInfo = getRoleInfo(user, query.getAppId());
        JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo = null;
        List<JuJuPrizeGrantRecordDO> receivedRewardList = null;
        if (roleInfo != null) {
            userCharacterEnhanceInfo = juJuCharacterEnhanceQueryRepository.getUserCharacterEnhanceInfoByRoleId(roleInfo.getRoleId(), query.getCardId(), juJuAchievementConfig.getDayVal());
            receivedRewardList = juJuCharacterEnhanceQueryRepository.getReceivedRewardList(roleInfo.getRoleId(), String.valueOf(query.getAppId()));
        }
        
        List<JuJuCharacterCardInfoDO> characterCardInfos = gameBDIPService.queryAllCharacterCardInfo(query.getAppId());
        if (CollectionUtils.isEmpty(characterCardInfos)) {
            return List.of();
        }
        List<SupportCardInfoDO> openedCardList = juJuCharacterEnhanceQueryRepository.getOpenedCardList();
        GameSDKInfoDO gameInfo = gameSDKInfoService.getGameInfo(query.getGameId());
        return assembler.toJuJuCharacterCardDTO(characterCardInfos, userCharacterEnhanceInfo, receivedRewardList, openedCardList, gameInfo);
    }
    
    @Override
    public JuJuUserCharacterRewardDTO getUserRewardList(UserCharacterEnhanceQuery query) {
        User user = Context.current().getUser();
        RoleInfoDO roleInfo = getRoleInfo(user, query.getAppId());
        JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo = null;
        List<JuJuPrizeGrantRecordDO> receivedRewardList = null;
        if (roleInfo == null) {
            log.warn("getUserRewardList roleInfo is null, user:{}", user);
            return new JuJuUserCharacterRewardDTO();
        }
        userCharacterEnhanceInfo = juJuCharacterEnhanceQueryRepository.getUserCharacterEnhanceInfoByRoleId(roleInfo.getRoleId(), query.getCardId(), juJuAchievementConfig.getDayVal());
        receivedRewardList = juJuCharacterEnhanceQueryRepository.getReceivedRewardList(roleInfo.getRoleId(), String.valueOf(query.getAppId()));
        
        return assembler.toJuJuUserCharacterRewardDTO(query.getCardId(), userCharacterEnhanceInfo, receivedRewardList);
    }
    
    private void updateCharacterEnhanceRoleName(List<JuJuCharacterEnhanceInfoDO> achievements) {
        
        try {
            CompletableFuture.allOf(achievements.stream()
                    .map(e -> CompletableFuture.runAsync(() -> {
                        try {
                            JuJuRoleBaseInfoDO juJuRoleBaseInfoDO = gameBDIPService.queryRoleBaseInfo(e.getServer().getAppId(), Long.valueOf(e.getRoleId()));
                            if (juJuRoleBaseInfoDO != null && juJuRoleBaseInfoDO.getRoleName() != null) {
                                log.info("updateCharacterEnhanceRoleName roleName:{}", juJuRoleBaseInfoDO.getRoleName());
                                e.setRoleName(juJuRoleBaseInfoDO.getRoleName());
                            }
                        } catch (Exception ex) {
                            log.warn("updateCharacterEnhanceRoleName Error querying role base info for roleId: {}", e.getRoleId(), ex);
                        }
                    }, customThreadPoolTaskExecutor).orTimeout(2, TimeUnit.SECONDS).exceptionally(ex -> {
                        if (ex instanceof TimeoutException) {
                            log.warn("updateCharacterEnhanceRoleName Timeout querying role base info for roleId: {}", e.getRoleId());
                        }
                        return null;
                    })).toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            log.warn("updateCharacterEnhanceRoleName updateRoleName error, achievements:{}", achievements, e);
        }
    }
    
    private RoleInfoDO getRoleInfo(User user, Integer appId) {
        if (user == null) {
            return null;
        }
        AccountInfo accountInfo = (AccountInfo) user;
        if (accountInfo.getCpUid() == null) {
            return null;
        }
        List<RoleInfoDO> roleInfoList = gameBDIPService.getRoleInfoList(appId, accountInfo.getCpUid());
        return roleInfoList.stream().findAny().orElse(null);
    }
}
