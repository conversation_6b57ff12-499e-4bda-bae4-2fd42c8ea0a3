package com.biligame.activity.community.application.query.impl;

import com.biligame.activity.community.application.query.JuJuCharacterEnhanceQueryService;
import com.biligame.activity.community.application.query.config.JuJuAchievementConfig;
import com.biligame.activity.community.application.query.dto.achievement.UserProfileDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuCharacterEnhanceDTO;
import com.biligame.activity.community.application.query.dto.character.UserCharacterEnhanceQuery;
import com.biligame.activity.community.application.query.repository.JuJuCharacterEnhanceQueryRepository;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuCharacterEnhanceInfoDO;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.RoleInfoDO;
import com.biligame.activity.community.infrastructure.rpc.dip.service.GameBDIPService;
import com.biligame.activity.community.infrastructure.rpc.meta.service.GameSDKInfoService;
import com.biligame.activity.community.shared.entity.AccountInfo;
import com.biligame.activity.community.shared.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pleiades.venus.context.Context;
import pleiades.venus.context.User;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/27 19:56
 * @description
 */
@Slf4j
@Service
public class JuJuCharacterEnhanceQueryServiceImpl implements JuJuCharacterEnhanceQueryService {
    
    @Resource
    private GameBDIPService gameBDIPService;
    
    @Resource
    private JuJuCharacterEnhanceQueryRepository juJuCharacterEnhanceQueryRepository;
    
    @Resource
    private GameSDKInfoService gameSDKInfoService;
    
    @Resource
    private JuJuAchievementConfig juJuAchievementConfig;
    
    private static final Integer DEFAULT_LIMIT_FOR_CHARACTER_ENHANCE = 100;
    
    @Override
    public JuJuCharacterEnhanceDTO getUserCharacterEnhanceInfo(UserCharacterEnhanceQuery query) {
        User user = Context.current().getUser();
        Long cpUid = ((AccountInfo) user).getCpUid();
        RoleInfoDO roleInfo = getRoleInfo(user, query.getAppId());
        JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo = null;
        if (roleInfo != null) {
            userCharacterEnhanceInfo = juJuCharacterEnhanceQueryRepository.getUserCharacterEnhanceInfo(roleInfo.getRoleId(), query.getCardId(), juJuAchievementConfig.getDayVal());
        }
        
        return null;
    }
    
    private RoleInfoDO getRoleInfo(User user, Integer appId) {
        if (user == null) {
            return null;
        }
        AccountInfo accountInfo = (AccountInfo) user;
        if (accountInfo.getCpUid() == null) {
            return null;
        }
        List<RoleInfoDO> roleInfoList = gameBDIPService.getRoleInfoList(appId, accountInfo.getCpUid());
        return roleInfoList.stream().findAny().orElse(null);
    }
}
