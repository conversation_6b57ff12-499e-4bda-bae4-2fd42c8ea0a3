package com.biligame.activity.community.application.query.assembler;

import com.biligame.activity.community.application.command.config.JuJuCharacterEnhanceConfig;
import com.biligame.activity.community.application.query.dto.character.JuJuCharacterCardDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuCharacterEnhanceDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuUserAwardDTO;
import com.biligame.activity.community.application.query.dto.character.JuJuUserCharacterRewardDTO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuCharacterEnhanceInfoDO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuPrizeGrantRecordDO;
import com.biligame.activity.community.application.query.repository.dataobject.SupportCardInfoDO;
import com.biligame.activity.community.infrastructure.enums.JuJuCharacterEnhanceTierEnum;
import com.biligame.activity.community.infrastructure.enums.PrizeGrantStatusEnum;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.JuJuCharacterCardInfoDO;
import com.biligame.activity.community.infrastructure.rpc.file.service.JuJuFileService;
import com.biligame.activity.community.infrastructure.rpc.meta.dataobject.GameSDKInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/5/28 14:22
 * @description
 */
@Slf4j
@Component
public class JuJuCharacterEnhanceAssembler {
    
    @Resource
    private JuJuFileService juJuFileService;
    
    @Resource
    private JuJuCharacterEnhanceConfig juJuCharacterEnhanceConfig;
    
    public JuJuCharacterEnhanceDTO toJuJuCharacterEnhanceDTO(JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo, List<JuJuCharacterEnhanceInfoDO> enhanceLeaderboard, List<JuJuCharacterEnhanceInfoDO> globalCharacterEnhanceLeaderboard) {
        return null;
    }
    
    public JuJuUserCharacterRewardDTO juJuUserCharacterRewardDTO() {
        return null;
    }
    
    public List<JuJuCharacterCardDTO> toJuJuCharacterCardDTO(List<JuJuCharacterCardInfoDO> characterCardInfos,
                                                             JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo,
                                                             List<JuJuPrizeGrantRecordDO> receivedRewardList,
                                                             List<SupportCardInfoDO> openedCardList,
                                                             GameSDKInfoDO gameSDKInfoDO) {
        Map<String, SupportCardInfoDO> supportCardInfoDOMap = openedCardList.stream().collect(Collectors.toMap(SupportCardInfoDO::getCardId, e -> e));
        
        return characterCardInfos.stream().map(e -> {
            String cardId = String.valueOf(e.getCardId());
            JuJuCharacterCardDTO juJuCharacterCardDTO = JuJuCharacterEnhanceConverter.INSTANCE.toJuJuCharacterCardDTO(e);
            juJuCharacterCardDTO.setImageUrl(juJuFileService.getCardIconImageUrl(cardId,gameSDKInfoDO));
            boolean isEnabledSupport = supportCardInfoDOMap.containsKey(cardId) && supportCardInfoDOMap.get(cardId).getStartTs() < System.currentTimeMillis() / 1000;
            juJuCharacterCardDTO.setEnableSupport(isEnabledSupport);
            juJuCharacterCardDTO.setHasUnreceivedReward(isEnabledSupport && !hasReceivedReward(cardId, userCharacterEnhanceInfo.getCombatTier(), receivedRewardList));
            return juJuCharacterCardDTO;
        }).collect(Collectors.toList());
    }
    
    private boolean hasReceivedReward(String cardId, String combatTier, List<JuJuPrizeGrantRecordDO> receivedRewardList) {
        return receivedRewardList.stream().anyMatch(e -> e.getCardId().equals(cardId) && e.getCombatTier().equals(combatTier));
    }
    
    public JuJuUserCharacterRewardDTO toJuJuUserCharacterRewardDTO(String cardId, JuJuCharacterEnhanceInfoDO userCharacterEnhanceInfo, List<JuJuPrizeGrantRecordDO> receivedRewardList) {
        JuJuUserCharacterRewardDTO juJuUserCharacterRewardDTO = new JuJuUserCharacterRewardDTO();
        juJuUserCharacterRewardDTO.setCardId(cardId);
        if (userCharacterEnhanceInfo == null) {
            log.warn("toJuJuUserCharacterRewardDTO userCharacterEnhanceInfo is null");
            return new JuJuUserCharacterRewardDTO();
        }
        juJuUserCharacterRewardDTO.setCurrentCombatTier(userCharacterEnhanceInfo.getCombatTier());
        juJuUserCharacterRewardDTO.setCombatScore(userCharacterEnhanceInfo.getCombatScore());
        List<JuJuCharacterEnhanceTierEnum> allCompletedTiers = JuJuCharacterEnhanceTierEnum.getAllCompletedTier(userCharacterEnhanceInfo.getCombatScore());
        List<JuJuUserAwardDTO> availableAwards = allCompletedTiers.stream().map(e -> buildJuJuUserAwardDTO(cardId, e.getTier())).collect(Collectors.toList());
        
        if (!CollectionUtils.isEmpty(availableAwards) && !CollectionUtils.isEmpty(receivedRewardList)) {
            Map<String, JuJuPrizeGrantRecordDO> juJuPrizeGrantRecordDOMap = receivedRewardList.stream().filter(e -> e.getCardId().equals(cardId)).collect(Collectors.toMap(JuJuPrizeGrantRecordDO::getCardId, e -> e));
            availableAwards.forEach(e -> {
                e.setClaimStatus(juJuPrizeGrantRecordDOMap.containsKey(e.getCombatTier()) ? PrizeGrantStatusEnum.CLAIMED.name() : PrizeGrantStatusEnum.UNCLAIMED.name());
            });
        }
        juJuUserCharacterRewardDTO.setAwards(availableAwards);
        return juJuUserCharacterRewardDTO;
    }
    
    private JuJuUserAwardDTO buildJuJuUserAwardDTO(String cardId, String combatTier) {
        
        JuJuCharacterEnhanceConfig.rewardConfig rewardConfig = juJuCharacterEnhanceConfig.getCombatTierRewardConfig().get(combatTier);
        
        JuJuUserAwardDTO juJuUserAwardDTO = new JuJuUserAwardDTO();
        juJuUserAwardDTO.setCardId(cardId);
        juJuUserAwardDTO.setCombatTier(combatTier);
        juJuUserAwardDTO.setPrizeId(rewardConfig!= null? rewardConfig.getRewardId() : null);
        return juJuUserAwardDTO;
    }
}
