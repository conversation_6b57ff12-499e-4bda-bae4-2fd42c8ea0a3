package com.biligame.activity.community.application.command.config;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/5/28 16:07
 * @description
 */
@Getter
@Setter
public class JuJuCharacterEnhanceConfig {
    
    private Map<String, rewardConfig> combatTierRewardConfig;
    
    @Getter
    @Setter
    public static class rewardConfig {
        // 战力等级奖励配置
        private String rewardId;
        private String combatTier;
        private String rewardDesc;
    }
    
    public rewardConfig getRewardConfig(String combatTier) {
        return combatTierRewardConfig.get(combatTier);
    }
}
