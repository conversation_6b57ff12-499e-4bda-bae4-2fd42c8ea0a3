package com.biligame.activity.community.application.query.dto.character;

import com.biligame.activity.community.application.query.dto.BaseDTO;
import com. biligame. activity. community. infrastructure. enums. JuJuCharacterEnhanceTierEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2025/5/28 14:58
 * @description
 */
@Getter
@Setter
public class JuJuUserAwardDTO extends BaseDTO {
    private static final long serialVersionUID = 3121657883656071561L;
    
    private String cardId;
    
    /**
     * @see JuJuCharacterEnhanceTierEnum
     */
    private String combatTier;
    
    private String prizeId;
    
    /**
     * 领取状态
     *
     */
    private String claimStatus;
}
