package com.biligame.activity.community.application.query.dto.character;

import com.biligame.activity.community.application.query.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2025/5/28 14:36
 * @description
 */
@Getter
@Setter
public class JuJuCharacterCardDTO extends BaseDTO {
    private static final long serialVersionUID = -8553773514521325263L;
    
    private String cardId;
    
    private String characterId;
    
    private String rarityType;
    
    private Integer attributeId;
    
    private Long displayedAt;
    
    private String imageUrl;
    
    private boolean enableSupport;
    
    // 是否有未领取的奖励
    private boolean hasUnreceivedReward;
}
