package com.biligame.activity.community.application.query.assembler;

import com.biligame.activity.community.application.query.dto.character.JuJuCharacterCardDTO;
import com.biligame.activity.community.infrastructure.rpc.dip.dataobject.JuJuCharacterCardInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2025/5/28 16:12
 * @description
 */
@Mapper
public interface JuJuCharacterEnhanceConverter {
    
    JuJuCharacterEnhanceConverter INSTANCE = Mappers.getMapper(JuJuCharacterEnhanceConverter.class);
    
    @Mappings({
            @Mapping(target = "enableSupport", ignore = true),
            @Mapping(target = "hasUnreceivedReward", ignore = true),
            @Mapping(target = "imageUrl", ignore = true),
            @Mapping(target = "displayedAt", ignore = true)
    })
    JuJuCharacterCardDTO toJuJuCharacterCardDTO(JuJuCharacterCardInfoDO characterCardInfoDO);
}
