package com.biligame.activity.community.infrastructure.db.converter;

import com.biligame.activity.community.application.query.repository.dataobject.*;
import com.biligame.activity.community.infrastructure.db.po.*;
import com.biligame.activity.community.shared.util.JacksonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/12/13 20:02
 * @description
 */
@Mapper
public interface JuJuMapperConverter {
    
    JuJuMapperConverter INSTANCE = Mappers.getMapper(JuJuMapperConverter.class);
    
    JuJuBattleEventStatDO toJuJuBattleEventStatDO(JujuBattleEventCompletionStatisticPO po);
    
    @Mappings({
            @Mapping(target = "roleName", ignore = true),
            @Mapping(target = "globalRank", ignore = true),
            @Mapping(target = "rank", ignore = true),
            @Mapping(target = "server", expression = "java(com.biligame.activity.community.infrastructure.enums.JuJuServerEnum.getByCpServer(po.getServer()))")
    })
    JuJuBattleEventAchievementDO toJuJuBattleEventAchievementDO(JujuBattleEventUserAchievementsPO po);
    
    @Mappings({
            @Mapping(target = "server", expression = "java(com.biligame.activity.community.infrastructure.enums.JuJuServerEnum.getByCpServer(po.getServer()))")
    })
    JuJuBattleEventAchievementDO tojuJuBattleEventAchievementDO(JujuBattleEventUserAchievementsV2PO po);
    
    @Mappings({
            @Mapping(target = "roleName", source = "roleName"),
            @Mapping(target = "server", expression = "java(com.biligame.activity.community.infrastructure.enums.JuJuServerEnum.getByCpServer(jujuIllusoryTowerUserAchievementsPO.getServer()))")
    })
    JuJuIllusoryTowerAchievementDO toJuJuIllusoryTowerAchievementDO(JujuIllusoryTowerUserAchievementsPO jujuIllusoryTowerUserAchievementsPO);
    
    @Mapping(target = "server", expression = "java(com.biligame.activity.community.infrastructure.enums.JuJuServerEnum.getByCpServer(po.getServer()))")
    JuJuMapEventAchievementDO toJuJuMapEventAchievementDO(JujuMapEventUserAchievementsPO po);
    
    @Mappings({
            @Mapping(target = "combatScore", source = "maxPower"),
            @Mapping(target = "combatTier", ignore = true),
            @Mapping(target = "server", expression = "java(com.biligame.activity.community.infrastructure.enums.JuJuServerEnum.getByCpServer(po.getServer()))")
    })
    JuJuCharacterEnhanceInfoDO toJuJuCharacterEnhanceInfoDO(JujuUserCharacterEnhanceInfoPO po);
    
    @Mappings({
            @Mapping(target = "cardId", expression = "java(getCardId(po))"),
            @Mapping(target = "combatTier", expression = "java(getCombatTier(po))")
    })
    JuJuPrizeGrantRecordDO toGameCommunityPrizeGrantRecordPO(GameCommunityPrizeGrantRecordPO po);
    
    default String getCardId(GameCommunityPrizeGrantRecordPO po) {
        Object cardId = JacksonUtils.parseObject(po.getExtInfo()).get("cardId");
        return String.valueOf(cardId);
    }
    
    default String getCombatTier(GameCommunityPrizeGrantRecordPO po) {
        Object combatTier = JacksonUtils.parseObject(po.getExtInfo()).get("combatTier");
        return String.valueOf(combatTier);
    }
}
