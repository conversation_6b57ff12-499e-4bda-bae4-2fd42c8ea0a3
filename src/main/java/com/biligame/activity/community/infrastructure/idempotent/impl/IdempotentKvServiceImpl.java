package com.biligame.activity.community.infrastructure.idempotent.impl;

import com.biligame.activity.community.infrastructure.db.mapper.IdempotentKvMapper;
import com.biligame.activity.community.infrastructure.db.po.IdempotentKvPO;
import com.biligame.activity.community.infrastructure.idempotent.IdempotentKvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025/5/27 12:30
 * @description 幂等KV存储服务实现
 */
@Slf4j
@Service
public class IdempotentKvServiceImpl implements IdempotentKvService {

    @Resource
    private IdempotentKvMapper idempotentKvMapper;

    @Override
    public boolean trySet(String key, String value) {
        if (!StringUtils.hasText(key) || !StringUtils.hasText(value)) {
            throw new IllegalArgumentException("key和value不能为空");
        }

        try {
            IdempotentKvPO record = new IdempotentKvPO();
            record.setIdempotentKey(key);
            record.setValue(value);
            record.setIsDeleted("N");

            Date now = new Date();
            record.setCtime(now);
            record.setMtime(now);

            int result = idempotentKvMapper.insert(record);

            if (result > 0) {
                log.info("幂等KV设置成功: key={}, value={}", key, value);
                return true;
            } else {
                log.warn("幂等KV设置失败: key={}, value={}", key, value);
                return false;
            }

        } catch (DuplicateKeyException e) {
            // 唯一键冲突，说明已存在
            log.info("幂等KV已存在: key={}", key);
            return false;
        } catch (Exception e) {
            log.error("幂等KV设置异常: key={}, value={}", key, value, e);
            throw new RuntimeException("幂等KV设置失败", e);
        }
    }

    @Override
    public String get(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }

        try {
            return idempotentKvMapper.selectValueByKey(key);
        } catch (Exception e) {
            log.error("获取幂等KV失败: key={}", key, e);
            return null;
        }
    }

    @Override
    public boolean exists(String key) {
        if (!StringUtils.hasText(key)) {
            return false;
        }

        try {
            return idempotentKvMapper.existsByKey(key);
        } catch (Exception e) {
            log.error("检查幂等KV存在性失败: key={}", key, e);
            return false;
        }
    }

    @Override
    public String buildKey(String... parts) {
        if (parts == null || parts.length == 0) {
            throw new IllegalArgumentException("构建key的参数不能为空");
        }

        StringBuilder keyBuilder = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            if (i > 0) {
                keyBuilder.append(":");
            }
            keyBuilder.append(parts[i] != null ? parts[i] : "null");
        }

        return keyBuilder.toString();
    }


}
