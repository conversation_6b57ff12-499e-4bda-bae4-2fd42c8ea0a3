package com.biligame.activity.community.infrastructure.config;

import com.biligame.activity.community.infrastructure.config.properties.SnowflakeIdProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Nonnull;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @create 2025/5/27 17:10
 * @description
 */
public class RedisOrientedSnowflakeIdSupport implements InitializingBean {


    private static final Logger log = LoggerFactory.getLogger(RedisOrientedSnowflakeIdSupport.class);
    public static final long CACHE_TIME_TWENTY_FIVE_MINUTE = 1500L;
    private static final long CACHE_TIME_THIRTY_MINUTE = 1800L;
    private static final long serverIdBits = 6L;
    private static final long instanceIdBits = 10L;
    private static final long sequenceBits = 6L;
    private static final long maxServerId = 63L;
    private static final long maxInstanceId = 1023L;
    private static final long maxSequence = 63L;
    private static final long timeBitsShift = 22L;
    private static final long serverIdBitsShift = 16L;
    private static final long instanceIdBitsShift = 6L;
    private final long startTime;
    private final String tenant;
    private final long serverId;
    private final RedisEnhanceOperatorSupport redisEnhanceOperatorSupport;
    private String ID_GENERATOR_INSTANCE_KEY;
    private volatile long instanceId;
    private volatile boolean initSign;
    private long sequence;
    private long lastTimestamp;
    
    private RedisOrientedSnowflakeIdSupport(@Nonnull String tenant, long serverId, RedisEnhanceOperatorSupport redisEnhanceOperatorSupport) {
        this.startTime = 1262275200000L;
        this.ID_GENERATOR_INSTANCE_KEY = "id.tenant.{0}.server.{1}.instance.{2}";
        this.instanceId = -1L;
        this.initSign = false;
        this.lastTimestamp = -1L;
        this.tenant = tenant;
        this.serverId = serverId;
        this.redisEnhanceOperatorSupport = redisEnhanceOperatorSupport;
    }
    
    public RedisOrientedSnowflakeIdSupport(@Nonnull SnowflakeIdProperties snowflakeIdProperties, RedisEnhanceOperatorSupport redisEnhanceOperatorSupport) {
        this(snowflakeIdProperties.getTenant(), (long)snowflakeIdProperties.getServerId(), redisEnhanceOperatorSupport);
    }
    
    public long getId() {
        return this.nextId();
    }
    
    public synchronized long nextId() {
        if (this.serverId >= 0L && this.instanceId >= 0L) {
            long timestamp = this.currentTime();
            if (timestamp < this.lastTimestamp) {
                throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", this.lastTimestamp - timestamp));
            } else {
                if (this.lastTimestamp == timestamp) {
                    this.sequence = this.sequence + 1L & 63L;
                    if (this.sequence == 0L) {
                        timestamp = this.blockTillNextMillis(this.lastTimestamp);
                    }
                } else {
                    this.sequence = 0L;
                }
                
                this.lastTimestamp = timestamp;
                return timestamp - 1262275200000L << 22 | this.serverId << 16 | this.instanceId << 6 | this.sequence;
            }
        } else {
            throw new IllegalArgumentException("目前不能生成唯一性ID,serverId:[" + this.serverId + "],instanceId:[" + this.instanceId + "]!");
        }
    }
    
    protected long blockTillNextMillis(long lastTimestamp) {
        long timestamp;
        for(timestamp = this.currentTime(); timestamp <= lastTimestamp; timestamp = this.currentTime()) {
        }
        
        return timestamp;
    }
    
    protected long currentTime() {
        return System.currentTimeMillis();
    }
    
    public void afterPropertiesSet() throws Exception {
        if (this.serverId <= 63L && this.serverId >= 0L) {
            if (!this.initSign) {
                this.initSign = true;
                ScheduledExecutorService scheduledService = Executors.newScheduledThreadPool(1);
                RegisterIdCreatorInstanceTask registerIdCreatorInstanceTask = new RegisterIdCreatorInstanceTask(this, this.redisEnhanceOperatorSupport);
                scheduledService.scheduleWithFixedDelay(registerIdCreatorInstanceTask, 0L, 1500L, TimeUnit.SECONDS);
            } else {
                log.info("已经初始化！");
            }
            
        } else {
            throw new IllegalArgumentException("serverId最小值为： 0,最大值为： 63");
        }
    }
    
    public long getStartTime() {
        this.getClass();
        return 1262275200000L;
    }
    
    public String getTenant() {
        return this.tenant;
    }
    
    public long getServerId() {
        return this.serverId;
    }
    
    public RedisEnhanceOperatorSupport getRedisEnhanceOperatorSupport() {
        return this.redisEnhanceOperatorSupport;
    }
    
    public String getID_GENERATOR_INSTANCE_KEY() {
        return this.ID_GENERATOR_INSTANCE_KEY;
    }
    
    public long getInstanceId() {
        return this.instanceId;
    }
    
    public boolean isInitSign() {
        return this.initSign;
    }
    
    public long getSequence() {
        return this.sequence;
    }
    
    public long getLastTimestamp() {
        return this.lastTimestamp;
    }
    
    public void setID_GENERATOR_INSTANCE_KEY(String ID_GENERATOR_INSTANCE_KEY) {
        this.ID_GENERATOR_INSTANCE_KEY = ID_GENERATOR_INSTANCE_KEY;
    }
    
    public void setInstanceId(long instanceId) {
        this.instanceId = instanceId;
    }
    
    public void setInitSign(boolean initSign) {
        this.initSign = initSign;
    }
    
    public void setSequence(long sequence) {
        this.sequence = sequence;
    }
    
    public void setLastTimestamp(long lastTimestamp) {
        this.lastTimestamp = lastTimestamp;
    }
    
    private class RegisterIdCreatorInstanceTask implements Runnable {
        private RedisOrientedSnowflakeIdSupport idGenerator;
        private RedisEnhanceOperatorSupport redisEnhanceOperatorSupport;
        
        private RegisterIdCreatorInstanceTask(RedisOrientedSnowflakeIdSupport idGenerator, RedisEnhanceOperatorSupport redisEnhanceOperatorSupport) {
            this.idGenerator = idGenerator;
            this.redisEnhanceOperatorSupport = redisEnhanceOperatorSupport;
        }
        
        public void run() {
            try {
                long srvId = this.idGenerator.getServerId();
                long currentInstanceId = this.idGenerator.getInstanceId();
                if (currentInstanceId < 0L) {
                    this.registerInstanceIdWithIpv4();
                } else {
                    String redisKey = MessageFormat.format(RedisOrientedSnowflakeIdSupport.this.ID_GENERATOR_INSTANCE_KEY, new Object[]{RedisOrientedSnowflakeIdSupport.this.tenant, String.valueOf(RedisOrientedSnowflakeIdSupport.this.serverId), String.valueOf(currentInstanceId)});
                    String ipResult = this.redisEnhanceOperatorSupport.getValOnSpringDataRedis(redisKey);
                    String ipResultLocal;
                    if ((ipResultLocal = this.getIPAddress()).equals(ipResult)) {
                        RedisOrientedSnowflakeIdSupport.log.info("服务[" + srvId + "]ID生成器：RegisterIdCreatorInstanceTask execute ip match,redisKey:{}, ipResultLocal:{}, ipResult:{}", new Object[]{redisKey, ipResultLocal, ipResult});
                        boolean result = this.redisEnhanceOperatorSupport.xxSetEXBaseOnSpringDataRedis(redisKey, ipResult, 1800L);
                        if (!result) {
                            RedisOrientedSnowflakeIdSupport.log.info("服务[" + srvId + "]ID生成器：" + currentInstanceId + "续约失败，等待重新注册");
                            this.registerInstanceIdWithIpv4();
                        } else {
                            RedisOrientedSnowflakeIdSupport.log.info("服务[" + srvId + "]ID生成器：" + currentInstanceId + "续约成功");
                        }
                    } else {
                        RedisOrientedSnowflakeIdSupport.log.info("服务[" + srvId + "]ID生成器：RegisterIdCreatorInstanceTask execute ip not match,redisKey:{}, ipResultLocal:{}, ipResult:{}", new Object[]{redisKey, ipResultLocal, ipResult});
                        this.registerInstanceIdWithIpv4();
                    }
                }
            } catch (Exception var12) {
                RedisOrientedSnowflakeIdSupport.log.error("Redis 出现异常！");
            } finally {
                if (this.idGenerator.getInstanceId() < 0L) {
                    this.idGenerator.setInitSign(false);
                }
                
            }
            
        }
        
        private int registerInstanceIdWithIpv4() {
            long ip4Value = this.getIp4LongValue();
            int instanceId = (int)(ip4Value % 1024L);
            int regInstanceId = this.registerInstanceId(instanceId, 1023, (v) -> {
                String redisKey = MessageFormat.format(RedisOrientedSnowflakeIdSupport.this.ID_GENERATOR_INSTANCE_KEY, new Object[]{RedisOrientedSnowflakeIdSupport.this.tenant, String.valueOf(RedisOrientedSnowflakeIdSupport.this.serverId), String.valueOf(v)});
                String ipAddress;
                boolean result = this.redisEnhanceOperatorSupport.nxSetEXBaseOnSpringDataRedis(redisKey, ipAddress = this.getIPAddress(), 1800L);
                RedisOrientedSnowflakeIdSupport.log.info("RegisterIdCreatorInstanceTask registerInstanceIdWithIpv4 redisKey:{}, ipAddress:{}, result:{}, applyParam:{}", new Object[]{redisKey, ipAddress, result, v});
                return result ? v : -1;
            });
            this.idGenerator.setInstanceId((long)regInstanceId);
            this.idGenerator.setInitSign(true);
            RedisOrientedSnowflakeIdSupport.log.info("服务[" + RedisOrientedSnowflakeIdSupport.this.serverId + "]注册了一个ID生成器：" + regInstanceId);
            return regInstanceId;
        }
        
        public int registerInstanceId(int basePoint, int max, Function<Integer, Integer> action) {
            for(int i = basePoint; i <= max; ++i) {
                int result = (Integer)action.apply(i);
                if (result > -1) {
                    return result;
                }
            }
            
            for(int i = 0; i < basePoint; ++i) {
                int result = (Integer)action.apply(i);
                if (result > -1) {
                    return result;
                }
            }
            
            return 0;
        }
        
        private String getIPAddress() {
            try {
                InetAddress address = InetAddress.getLocalHost();
                return address.getHostAddress();
            } catch (UnknownHostException e) {
                RedisOrientedSnowflakeIdSupport.log.error("RegisterIdCreatorInstanceTask getIPAddress error, msg:{}", e.getMessage(), e);
                return "0";
            }
        }
        
        private long getIp4LongValue() {
            try {
                InetAddress inetAddress = Inet4Address.getLocalHost();
                byte[] ip = inetAddress.getAddress();
                return (long)Math.abs(ip[0] << 24 | ip[1] << 16 | ip[2] << 8 | ip[3]);
            } catch (Exception ex) {
                RedisOrientedSnowflakeIdSupport.log.error("RegisterIdCreatorInstanceTask getIp4LongValue error, msg:{}", ex.getMessage(), ex);
                return 0L;
            }
        }
    }
}
