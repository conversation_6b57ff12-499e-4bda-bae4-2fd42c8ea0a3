package com.biligame.activity.community.infrastructure.rpc.dip.dataobject;

import com.biligame.activity.community.shared.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/12/10 17:58
 * @description
 */
@Getter
@Setter
public class JuJuCharacterCardInfoDO extends BaseDO {
    
    private static final long serialVersionUID = -8714564140209806920L;
    
    private Long cardId;
    
    private Long characterId;
    
    /**
     * 稀有度
     */
    private RarityTypeEnum rarityType;
    
    /**
     * 卡片类型, 1-体术, 2-术式
     */
//    private CardTypeEnum cardType;
//
//    private Integer roleType;
//
    /**
     * 特性
     * 3-幻，4-影，5-夜，6-行
     */
    private Integer attributeId;
    
    @Getter
    @AllArgsConstructor
    public enum RarityTypeEnum {
        R(1),
        SR(2),
        SSR(3),
        ;
        private final Integer code;
        
        public static RarityTypeEnum getByCode(Integer code) {
            return Arrays.stream(RarityTypeEnum.values()).filter(e -> e.code.equals(code)).findFirst().orElse(null);
        }
    }
    
    @Getter
    @AllArgsConstructor
    public enum CardTypeEnum {
        PHYSICAL(1),
        MAGIC(2),
        BALANCE(3)
        ;
        
        private final Integer code;
        
        public static CardTypeEnum getByCode(Integer code) {
            return Arrays.stream(CardTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        }
    }
}
