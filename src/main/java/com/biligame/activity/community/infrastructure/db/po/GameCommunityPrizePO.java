package com.biligame.activity.community.infrastructure.db.po;

import java.util.Date;
import lombok.Data;

/**
 * 游戏社区服务奖品信息
 * @TableName game_community_prize
 */
@Data
public class GameCommunityPrizePO {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 奖品ID
     */
    private String prizeId;

    /**
     * 所属业务
     */
    private String bizType;

    /**
     * 奖品类型
     */
    private String prizeType;

    /**
     * 图片地址
     */
    private String prizeImage;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 奖品描述
     */
    private String description;

    /**
     * 其他信息，json
     */
    private String extInfo;

    /**
     * 删除标记（Y/N）
     */
    private String isDeleted;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}