package com.biligame.activity.community.infrastructure.config;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2025/5/27 17:12
 * @description Redis增强操作支持类 - 基于Redisson实现
 */
public class RedisEnhanceOperatorSupport {
    private static final Logger log = LoggerFactory.getLogger(RedisEnhanceOperatorSupport.class);
    private static final String BZ_EMPTY = "";
    private static final String REDIS_OPERATE_RESULT = "OK";
    private final RedissonClient redissonClient;

    public RedisEnhanceOperatorSupport(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * SET key value NX EX seconds - 设置键值对，仅当键不存在时，并设置过期时间（秒）
     * @param key 键
     * @param value 值
     * @param time 过期时间（秒）
     * @return 是否设置成功
     */
    public boolean nxSetEXBaseOnSpringDataRedis(String key, Object value, long time) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            boolean result = bucket.trySet(value, time, TimeUnit.SECONDS);
            log.info("==>>>RedisEnhanceOperatorSupport nxSetEXBaseOnRedisson, execResult:{}, key:{}, value:{}", result, key, value);
            return result;
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport nxSetEXBaseOnRedisson failed, key:{}, value:{}, unidentified exception...,{}", key, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * SET key value NX PX milliseconds - 设置键值对，仅当键不存在时，并设置过期时间（毫秒）
     * @param key 键
     * @param value 值
     * @param time 过期时间（毫秒）
     * @return 是否设置成功
     */
    public boolean nxSetPXBaseOnSpringDataRedis(String key, Object value, long time) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            boolean result = bucket.trySet(value, time, TimeUnit.MILLISECONDS);
            log.info("==>>>RedisEnhanceOperatorSupport nxSetPXBaseOnRedisson, execResult:{}, key:{}, value:{}", result, key, value);
            return result;
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport nxSetPXBaseOnRedisson failed, key:{}, value:{}, unidentified exception...,{}", key, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * SET key value XX EX seconds - 设置键值对，仅当键已存在时，并设置过期时间（秒）
     * @param key 键
     * @param value 值
     * @param time 过期时间（秒）
     * @return 是否设置成功
     */
    public boolean xxSetEXBaseOnSpringDataRedis(String key, Object value, long time) {
        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            // Redisson没有直接的XX操作，需要先检查键是否存在
            if (bucket.isExists()) {
                bucket.set(value, time, TimeUnit.SECONDS);
                log.info("==>>>RedisEnhanceOperatorSupport xxSetEXBaseOnRedisson, execResult:true, key:{}, value:{}", key, value);
                return true;
            } else {
                log.info("==>>>RedisEnhanceOperatorSupport xxSetEXBaseOnRedisson, execResult:false (key not exists), key:{}, value:{}", key, value);
                return false;
            }
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport xxSetEXBaseOnRedisson failed, key:{}, value:{}, unidentified exception...,{}", key, value, e.getMessage(), e);
            return false;
        }
    }

    /**
     * GET key - 获取键对应的值
     * @param key 键
     * @return 值，不存在返回null
     */
    public String getValOnSpringDataRedis(String key) {
        String value = null;

        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            Object result = bucket.get();
            value = result != null ? result.toString() : null;
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport getValOnRedisson [{}] error...,msg:{}", key, e.getMessage(), e);
        }

        log.info("==>>>RedisEnhanceOperatorSupport getValOnRedisson, key:{}, value:{}", key, value);
        return value;
    }

    /**
     * DEL key - 删除键
     * @param key 键
     * @return 是否删除成功
     */
    public boolean deleteKey(String key) {
        boolean execResult = false;

        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            execResult = bucket.delete();
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport deleteKey [{}] error...,msg:{}", key, e.getMessage(), e);
        }

        log.info("==>>>RedisEnhanceOperatorSupport deleteKey, key:{}, result:{}", key, execResult);
        return execResult;
    }

    /**
     * DEL key1 key2 ... - 批量删除键
     * @param keys 键集合
     * @return 删除的键数量
     */
    public Long delGroupKey(Collection<String> keys) {
        try {
            if (keys == null || keys.isEmpty()) {
                return 0L;
            }
            String[] keyArray = keys.toArray(new String[0]);
            long deletedCount = redissonClient.getKeys().delete(keyArray);
            log.info("==>>>RedisEnhanceOperatorSupport delGroupKey, keys:{}, deletedCount:{}", keys, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("==>>>RedisEnhanceOperatorSupport delGroupKey [{}] error...,msg:{}", keys, e.getMessage(), e);
            return null;
        }
    }
}
