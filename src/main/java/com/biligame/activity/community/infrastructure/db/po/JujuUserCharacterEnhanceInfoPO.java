package com.biligame.activity.community.infrastructure.db.po;

import java.util.Date;
import lombok.Data;

/**
 * 咒术角色强化排行数据
 * @TableName juju_user_character_enhance_info
 */
@Data
public class JujuUserCharacterEnhanceInfoPO {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 区服: sgp,hk,fra,sp,sv,va,kr
     */
    private String server;

    /**
     * card_id
     */
    private String cardId;

    /**
     * role_id
     */
    private String roleId;

    /**
     * 玩家昵称
     */
    private String roleName;

    /**
     * cp_uid
     */
    private String cpUid;

    /**
     * 角色战力
     */
    private Integer maxPower;

    /**
     * 本服排名1-N
     */
    private Integer rank;

    /**
     * 全服排名1-N
     */
    private Integer globalRank;

    /**
     * 数据日期 格式:yyyyMMdd
     */
    private String ds;

    /**
     * 删除标记（Y/N）
     */
    private String isDeleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}