package com.biligame.activity.community.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2025/5/28 15:12
 * @description 咒术角色强化战力档位
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum JuJuCharacterEnhanceTierEnum {
    
    S("S", 9000, 18000),
    A("A", 5000, 9000),
    B("B", 1200, 5000),
    C("C", 500, 1200),
    D("D", 250, 500),
    E("E", 0, 250)
    ;
    
    private final String tier;
    
    /**
     * 最小战力, 不包含
     */
    private final Integer minScore;
    
    /**
     * 最大战力, 包含
     */
    private final Integer maxScore;
    
    public static JuJuCharacterEnhanceTierEnum getByScore(Integer score) {
        for (JuJuCharacterEnhanceTierEnum value : JuJuCharacterEnhanceTierEnum.values()) {
            if (score > value.getMinScore() && score <= value.getMaxScore()) {
                return value;
            }
        }
        if (score > S.maxScore) {
            log.info("JuJuCharacterEnhanceTierEnum getByScore score:{} is greater than S tier", score);
            return S;
        }
        return null;
    }
    
    public static List<JuJuCharacterEnhanceTierEnum> getAllCompletedTier(Integer score) {
        return Stream.of(values()).filter(e -> score > e.getMinScore()).collect(Collectors.toList());
    }
}
