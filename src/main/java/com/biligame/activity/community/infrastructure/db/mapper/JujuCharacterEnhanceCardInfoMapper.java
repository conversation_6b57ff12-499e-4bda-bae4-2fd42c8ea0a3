package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO;

/**
* <AUTHOR>
* @description 针对表【juju_character_enhance_card_info(咒术角色卡片应援信息)】的数据库操作Mapper
* @createDate 2025-05-28 10:37:44
* @Entity com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO
*/
public interface JujuCharacterEnhanceCardInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(JujuCharacterEnhanceCardInfoPO record);

    int insertSelective(JujuCharacterEnhanceCardInfoPO record);

    JujuCharacterEnhanceCardInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JujuCharacterEnhanceCardInfoPO record);

    int updateByPrimaryKey(JujuCharacterEnhanceCardInfoPO record);

}
