package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO;

/**
* <AUTHOR>
* @description 针对表【game_community_prize(游戏社区服务奖品信息)】的数据库操作Mapper
* @createDate 2025-05-28 10:37:44
* @Entity com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO
*/
public interface GameCommunityPrizeMapper {

    int deleteByPrimaryKey(Long id);

    int insert(GameCommunityPrizePO record);

    int insertSelective(GameCommunityPrizePO record);

    GameCommunityPrizePO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GameCommunityPrizePO record);

    int updateByPrimaryKey(GameCommunityPrizePO record);

}
