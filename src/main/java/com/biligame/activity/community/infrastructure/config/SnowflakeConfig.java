package com.biligame.activity.community.infrastructure.config;

import com.biligame.activity.community.infrastructure.config.properties.SnowflakeIdProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2025/5/27 17:25
 * @description
 */
@Configuration
public class SnowflakeConfig {
    private static final String DEFAULT_TENANT = "game-activity-community";
    
    @Bean
    public RedisOrientedSnowflakeIdSupport juJuPrizeGrantRecordNoGenerator(RedisEnhanceOperatorSupport redisEnhanceOperatorSupport) {
        return new RedisOrientedSnowflakeIdSupport(getSnowflakeIdPropertiesDefinition(1), redisEnhanceOperatorSupport);
    }
    
    private SnowflakeIdProperties getSnowflakeIdPropertiesDefinition(int serverId) {
        SnowflakeIdProperties snowflakeIdProperties = new SnowflakeIdProperties();
        snowflakeIdProperties.setTenant(DEFAULT_TENANT);
        snowflakeIdProperties.setServerId(serverId);
        return snowflakeIdProperties;
    }
}
