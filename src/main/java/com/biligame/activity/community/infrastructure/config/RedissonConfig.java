package com.biligame.activity.community.infrastructure.config;


import com.biligame.activity.community.infrastructure.config.properties.RedissonProperties;
import com.biligame.activity.community.shared.util.LocalDateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.opentelemetry.api.OpenTelemetry;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @create 2024/11/29 17:50
 * @description
 */
@Configuration
public class RedissonConfig {


	@Bean
	@ConfigurationProperties(prefix = "redis.redisson")
	public RedissonProperties redissonProperties(){
		return new RedissonProperties();
	}

	@Bean(destroyMethod = "shutdown")
	public RedissonClient redissonClient(RedissonProperties redissonProperties, OpenTelemetry openTelemetry) {
		
		Config config = new Config();
		config.useSingleServer()
				.setAddress(redissonProperties.getAddress())
				.setPassword(StringUtils.defaultIfEmpty(redissonProperties.getPassword(), null))
				.setTimeout(redissonProperties.getTimeout())
				.setDatabase(redissonProperties.getDatabase())
				.setConnectionPoolSize(redissonProperties.getConnectionPoolSize())
				.setConnectTimeout(redissonProperties.getConnectionTimeOut())
				.setClientName(null)
		;
		ObjectMapper om = getOm();
		config.setCodec(new JsonJacksonCodec(om));
		config.setCluster(redissonProperties.getCluster());
		return Redisson.create(config,openTelemetry);
	}

	@Bean
	public RedisEnhanceOperatorSupport redisEnhanceOperatorSupport(RedissonClient redissonClient) {
		return new RedisEnhanceOperatorSupport(redissonClient);
	}

	private ObjectMapper getOm() {
		ObjectMapper mapper = new ObjectMapper();
		// // Date类型格式化
		// mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
		// java8 新的时间类 格式化
		JavaTimeModule javaTimeModule = new JavaTimeModule();
		//  注意 同一个时间类型不能使用两个格式化格式 后一个会覆盖前一个
		javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(LocalDateUtil.DATE_TIME_MILLS_FORMATTER));
		javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(LocalDateUtil.DATE_TIME_MILLS_FORMATTER));
		javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(LocalDateUtil.DATE_FORMATTER));
		javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(LocalDateUtil.DATE_FORMATTER));
		javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(LocalDateUtil.TIME_MILLS_FORMATTER));
		javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(LocalDateUtil.TIME_MILLS_FORMATTER));
		//  自己写的时间模块注册进ObjectMapper
		mapper.registerModule(javaTimeModule);
		return mapper;
	}

}