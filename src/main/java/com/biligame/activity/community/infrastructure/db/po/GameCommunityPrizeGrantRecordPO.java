package generator.domain;

import java.util.Date;
import lombok.Data;

/**
 * 游戏社区服务发奖记录
 * @TableName game_community_prize_grant_record
 */
@Data
public class GameCommunityPrizeGrantRecordPO {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 奖励ID
     */
    private String prizeId;

    /**
     * 奖励类型
     */
    private String prizeType;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务ID，唯一
     */
    private String bizNo;

    /**
     * uid
     */
    private String uid;

    /**
     * 区服ID
     */
    private String serverId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 游戏基础ID
     */
    private String gameBaseId;

    /**
     * 游戏ID
     */
    private String gameId;

    /**
     * 其他信息，json
     */
    private String extInfo;

    /**
     * 删除标记（Y/N）
     */
    private String isDeleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}