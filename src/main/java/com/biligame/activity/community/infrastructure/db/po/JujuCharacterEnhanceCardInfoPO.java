package com.biligame.activity.community.infrastructure.db.po;

import java.util.Date;
import lombok.Data;

/**
 * 咒术角色卡片应援信息
 * @TableName juju_character_enhance_card_info
 */
@Data
public class JujuCharacterEnhanceCardInfoPO {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 奖品ID
     */
    private String cardId;

    /**
     * 开放时间
     */
    private Date startTime;

    /**
     * 开放时间，s
     */
    private Long startTs;

    /**
     * 删除标记（Y/N）
     */
    private String isDeleted;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}