package com.biligame.activity.community.infrastructure.db.repository;

import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.biligame.activity.community.application.query.repository.JuJuCharacterEnhanceQueryRepository;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuCharacterEnhanceInfoDO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuIllusoryTowerAchievementDO;
import com.biligame.activity.community.application.query.repository.dataobject.JuJuPrizeGrantRecordDO;
import com.biligame.activity.community.application.query.repository.dataobject.SupportCardInfoDO;
import com.biligame.activity.community.domain.character.repository.JuJuCharacterEnhanceRepository;
import com.biligame.activity.community.infrastructure.db.converter.JuJuMapperConverter;
import com.biligame.activity.community.infrastructure.db.mapper.GameCommunityPrizeGrantRecordMapper;
import com.biligame.activity.community.infrastructure.db.mapper.JujuChapterReferenceMapper;
import com.biligame.activity.community.infrastructure.db.mapper.JujuCharacterEnhanceCardInfoMapper;
import com.biligame.activity.community.infrastructure.db.mapper.JujuUserCharacterEnhanceInfoMapper;
import com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO;
import com.biligame.activity.community.infrastructure.db.po.JujuBattleEventUserAchievementsV2PO;
import com.biligame.activity.community.infrastructure.db.po.JujuIllusoryTowerUserAchievementsPO;
import com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO;
import com.biligame.activity.community.infrastructure.enums.PrizeBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/5/28 10:40
 * @description
 */
@Slf4j
@Repository
public class JuJuCharacterEnhanceRepositoryImpl implements JuJuCharacterEnhanceRepository, JuJuCharacterEnhanceQueryRepository {
    
    @Resource
    private JujuUserCharacterEnhanceInfoMapper jujuUserCharacterEnhanceInfoMapper;
    
    @Resource
    private JujuCharacterEnhanceCardInfoMapper jujuCharacterEnhanceCardInfoMapper;
    
    @Resource
    private GameCommunityPrizeGrantRecordMapper gameCommunityPrizeGrantRecordMapper;
    
    @Override
    public List<SupportCardInfoDO> getOpenedCardList() {
        return List.of();
    }
    
    @Override
    public List<JuJuPrizeGrantRecordDO> getReceivedRewardList(String roleId, String appId) {
        List<GameCommunityPrizeGrantRecordPO> data = gameCommunityPrizeGrantRecordMapper.selectByRoleIdAndBizTypeAndServer(roleId, PrizeBizTypeEnum.CHARACTER_ENHANCE.name(), appId);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.stream().map(JuJuMapperConverter.INSTANCE::toGameCommunityPrizeGrantRecordPO).collect(Collectors.toList());
    }
    
    @Override
    @CachePenetrationProtect
    @Cached(name = "getUserCharacterEnhanceInfoByCpUid", key = "':' + #cpUid + ':' + #cardId + ':' + #ds", expire = 10, localExpire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH)
    public JuJuCharacterEnhanceInfoDO getUserCharacterEnhanceInfoByCpUid(String cpUid, String cardId, String ds) {
        List<JujuUserCharacterEnhanceInfoPO> pos = jujuUserCharacterEnhanceInfoMapper.selectByCpUidIdAndCardIdAndDs(cpUid, cardId, ds);
        if (pos == null || pos.isEmpty()) {
            return null;
        }
        
        JujuUserCharacterEnhanceInfoPO po = pos.stream().max(Comparator.comparing(JujuUserCharacterEnhanceInfoPO::getDs)).orElse(null);
        JuJuCharacterEnhanceInfoDO juJuCharacterEnhanceInfoDO = JuJuMapperConverter.INSTANCE.toJuJuCharacterEnhanceInfoDO(po);
        juJuCharacterEnhanceInfoDO.setRoleName(po.getRoleName());
        return juJuCharacterEnhanceInfoDO;
    }
    
    @Override
    @CachePenetrationProtect
    @Cached(name = "getUserCharacterEnhanceInfoByRoleId", key = "':' + #roleId + ':' + #cardId + ':' + #ds", expire = 10, localExpire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH)
    public JuJuCharacterEnhanceInfoDO getUserCharacterEnhanceInfoByRoleId(String roleId, String cardId, String ds) {
        
        List<JujuUserCharacterEnhanceInfoPO> pos = jujuUserCharacterEnhanceInfoMapper.selectByRoleIdAndCardIdAndDs(roleId, cardId, ds);
        if (pos == null || pos.isEmpty()) {
            return null;
        }
        
        JujuUserCharacterEnhanceInfoPO po = pos.stream().max(Comparator.comparing(JujuUserCharacterEnhanceInfoPO::getDs)).orElse(null);
        JuJuCharacterEnhanceInfoDO juJuCharacterEnhanceInfoDO = JuJuMapperConverter.INSTANCE.toJuJuCharacterEnhanceInfoDO(po);
        juJuCharacterEnhanceInfoDO.setRoleName(po.getRoleName());
        return juJuCharacterEnhanceInfoDO;
    }
    
    @Override
    @CachePenetrationProtect
    @Cached(name = "getCharacterEnhanceLeaderboard", key = "':' + #cardId + ':' + #server + ':' + #limit + ':' + #ds", expire = 10, localExpire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH)
    public List<JuJuCharacterEnhanceInfoDO> getCharacterEnhanceLeaderboard(String cardId, String server, Integer limit, String ds) {
        List<JujuUserCharacterEnhanceInfoPO> pos = jujuUserCharacterEnhanceInfoMapper.selectByCardIdAndServerAndRank(cardId, server, limit, ds);
        
        Collection<JujuUserCharacterEnhanceInfoPO> distinctPOs = pos.stream().collect(Collectors.toMap(
                JujuUserCharacterEnhanceInfoPO::getRank,
                Function.identity(),
                (e1, e2) -> e1.getDs().compareTo(e2.getDs()) >=0 ? e1 : e2
        )).values();
        
        if (distinctPOs.size() != pos.size()) {
            log.info("getCharacterEnhanceLeaderboard distinct size not equal, pos :{}, distinct :{}", pos, distinctPOs);
        }
        
        return distinctPOs.stream().map(JuJuMapperConverter.INSTANCE::toJuJuCharacterEnhanceInfoDO).collect(Collectors.toList());
    }
    
    @Override
    @CachePenetrationProtect
    @Cached(name = "getGlobalCharacterEnhanceLeaderboard", key = "':' + #cardId + ':' + #limit + ':' + #ds", expire = 10, localExpire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH)
    public List<JuJuCharacterEnhanceInfoDO> getGlobalCharacterEnhanceLeaderboard(String cardId, Integer limit, String ds) {
        List<JujuUserCharacterEnhanceInfoPO> pos = jujuUserCharacterEnhanceInfoMapper.selectByCardIdAndGlobalRank(cardId, limit, ds);
        
        Collection<JujuUserCharacterEnhanceInfoPO> distinctPOs = pos.stream().collect(Collectors.toMap(
                JujuUserCharacterEnhanceInfoPO::getGlobalRank,
                Function.identity(),
                (e1, e2) -> e1.getDs().compareTo(e2.getDs()) >=0 ? e1 : e2
        )).values();
        
        if (distinctPOs.size() != pos.size()) {
            log.info("getBattleEventGlobalAchievementLeaderboard distinct size not equal, pos :{}, distinct :{}", pos, distinctPOs);
        }
        
        return distinctPOs.stream().map(JuJuMapperConverter.INSTANCE::toJuJuCharacterEnhanceInfoDO).collect(Collectors.toList());    }
}
