package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO;

/**
* <AUTHOR>
* @description 针对表【game_community_prize_grant_record(游戏社区服务发奖记录)】的数据库操作Mapper
* @createDate 2025-05-28 10:37:44
* @Entity com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO
*/
public interface GameCommunityPrizeGrantRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(GameCommunityPrizeGrantRecordPO record);

    int insertSelective(GameCommunityPrizeGrantRecordPO record);

    GameCommunityPrizeGrantRecordPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GameCommunityPrizeGrantRecordPO record);

    int updateByPrimaryKey(GameCommunityPrizeGrantRecordPO record);

}
