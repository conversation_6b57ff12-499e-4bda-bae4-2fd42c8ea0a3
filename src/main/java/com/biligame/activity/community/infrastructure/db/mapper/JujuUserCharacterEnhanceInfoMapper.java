package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【juju_user_character_enhance_info(咒术角色强化排行数据)】的数据库操作Mapper
* @createDate 2025-05-28 10:37:44
* @Entity com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO
*/
public interface JujuUserCharacterEnhanceInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(JujuUserCharacterEnhanceInfoPO record);

    int insertSelective(JujuUserCharacterEnhanceInfoPO record);

    JujuUserCharacterEnhanceInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JujuUserCharacterEnhanceInfoPO record);

    int updateByPrimaryKey(JujuUserCharacterEnhanceInfoPO record);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByRoleIdAndCardIdAndDs(@Param("roleId") String roleId, @Param("cardId") String cardId, @Param("ds") String ds);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByCardIdAndServerAndRank(@Param("cardId") String cardId,@Param("server") String server, @Param("limit") Integer limit, @Param("ds") String ds);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByCardIdAndGlobalRank(@Param("cardId") String cardId,@Param("limit") Integer limit, @Param("ds") String ds);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByCpUidIdAndCardIdAndDs(@Param("cpUid") String cpUid,@Param("cardId") String cardId,@Param("ds") String ds);
}
