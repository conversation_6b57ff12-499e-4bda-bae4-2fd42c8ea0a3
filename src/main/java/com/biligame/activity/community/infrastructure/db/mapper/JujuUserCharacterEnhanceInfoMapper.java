package com.biligame.activity.community.infrastructure.db.mapper;

import com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【juju_user_character_enhance_info(咒术角色强化排行数据)】的数据库操作Mapper
* @createDate 2025-05-28 10:37:44
* @Entity com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO
*/
public interface JujuUserCharacterEnhanceInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(JujuUserCharacterEnhanceInfoPO record);

    int insertSelective(JujuUserCharacterEnhanceInfoPO record);

    JujuUserCharacterEnhanceInfoPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JujuUserCharacterEnhanceInfoPO record);

    int updateByPrimaryKey(JujuUserCharacterEnhanceInfoPO record);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByRoleIdAndCardIdAndDs(String roleId, String cardId, String ds);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByCardIdAndServerAndRank(String cardId, String server, Integer limit, String ds);
    
    List<JujuUserCharacterEnhanceInfoPO> selectByCardIdAndGlobalRank(String cardId, Integer limit, String ds);
}
