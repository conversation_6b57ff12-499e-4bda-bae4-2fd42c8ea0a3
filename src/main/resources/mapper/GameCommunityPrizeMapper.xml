<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biligame.activity.community.infrastructure.db.mapper.GameCommunityPrizeMapper">

    <resultMap id="BaseResultMap" type="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="prizeId" column="prize_id" jdbcType="VARCHAR"/>
            <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
            <result property="prizeType" column="prize_type" jdbcType="VARCHAR"/>
            <result property="prizeImage" column="prize_image" jdbcType="VARCHAR"/>
            <result property="prizeName" column="prize_name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,prize_id,biz_type,
        prize_type,prize_image,prize_name,
        description,ext_info,is_deleted,
        creator,ctime,mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from game_community_prize
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from game_community_prize
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO" useGeneratedKeys="true">
        insert into game_community_prize
        ( id,prize_id,biz_type
        ,prize_type,prize_image,prize_name
        ,description,ext_info,is_deleted
        ,creator,ctime,mtime
        )
        values (#{id,jdbcType=BIGINT},#{prizeId,jdbcType=VARCHAR},#{bizType,jdbcType=VARCHAR}
        ,#{prizeType,jdbcType=VARCHAR},#{prizeImage,jdbcType=VARCHAR},#{prizeName,jdbcType=VARCHAR}
        ,#{description,jdbcType=VARCHAR},#{extInfo,jdbcType=VARCHAR},#{isDeleted,jdbcType=CHAR}
        ,#{creator,jdbcType=VARCHAR},#{ctime,jdbcType=TIMESTAMP},#{mtime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO" useGeneratedKeys="true">
        insert into game_community_prize
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="prizeId != null">prize_id,</if>
                <if test="bizType != null">biz_type,</if>
                <if test="prizeType != null">prize_type,</if>
                <if test="prizeImage != null">prize_image,</if>
                <if test="prizeName != null">prize_name,</if>
                <if test="description != null">description,</if>
                <if test="extInfo != null">ext_info,</if>
                <if test="isDeleted != null">is_deleted,</if>
                <if test="creator != null">creator,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="prizeId != null">#{prizeId,jdbcType=VARCHAR},</if>
                <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
                <if test="prizeType != null">#{prizeType,jdbcType=VARCHAR},</if>
                <if test="prizeImage != null">#{prizeImage,jdbcType=VARCHAR},</if>
                <if test="prizeName != null">#{prizeName,jdbcType=VARCHAR},</if>
                <if test="description != null">#{description,jdbcType=VARCHAR},</if>
                <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=CHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO">
        update game_community_prize
        <set>
                <if test="prizeId != null">
                    prize_id = #{prizeId,jdbcType=VARCHAR},
                </if>
                <if test="bizType != null">
                    biz_type = #{bizType,jdbcType=VARCHAR},
                </if>
                <if test="prizeType != null">
                    prize_type = #{prizeType,jdbcType=VARCHAR},
                </if>
                <if test="prizeImage != null">
                    prize_image = #{prizeImage,jdbcType=VARCHAR},
                </if>
                <if test="prizeName != null">
                    prize_name = #{prizeName,jdbcType=VARCHAR},
                </if>
                <if test="description != null">
                    description = #{description,jdbcType=VARCHAR},
                </if>
                <if test="extInfo != null">
                    ext_info = #{extInfo,jdbcType=VARCHAR},
                </if>
                <if test="isDeleted != null">
                    is_deleted = #{isDeleted,jdbcType=CHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizePO">
        update game_community_prize
        set 
            prize_id =  #{prizeId,jdbcType=VARCHAR},
            biz_type =  #{bizType,jdbcType=VARCHAR},
            prize_type =  #{prizeType,jdbcType=VARCHAR},
            prize_image =  #{prizeImage,jdbcType=VARCHAR},
            prize_name =  #{prizeName,jdbcType=VARCHAR},
            description =  #{description,jdbcType=VARCHAR},
            ext_info =  #{extInfo,jdbcType=VARCHAR},
            is_deleted =  #{isDeleted,jdbcType=CHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
