<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biligame.activity.community.infrastructure.db.mapper.JujuCharacterEnhanceCardInfoMapper">

    <resultMap id="BaseResultMap" type="com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cardId" column="card_id" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="startTs" column="start_ts" jdbcType="BIGINT"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,card_id,start_time,
        start_ts,is_deleted,creator,
        ctime,mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from juju_character_enhance_card_info
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from juju_character_enhance_card_info
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO" useGeneratedKeys="true">
        insert into juju_character_enhance_card_info
        ( id,card_id,start_time
        ,start_ts,is_deleted,creator
        ,ctime,mtime)
        values (#{id,jdbcType=BIGINT},#{cardId,jdbcType=VARCHAR},#{startTime,jdbcType=TIMESTAMP}
        ,#{startTs,jdbcType=BIGINT},#{isDeleted,jdbcType=CHAR},#{creator,jdbcType=VARCHAR}
        ,#{ctime,jdbcType=TIMESTAMP},#{mtime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO" useGeneratedKeys="true">
        insert into juju_character_enhance_card_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="cardId != null">card_id,</if>
                <if test="startTime != null">start_time,</if>
                <if test="startTs != null">start_ts,</if>
                <if test="isDeleted != null">is_deleted,</if>
                <if test="creator != null">creator,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
                <if test="startTime != null">#{startTime,jdbcType=TIMESTAMP},</if>
                <if test="startTs != null">#{startTs,jdbcType=BIGINT},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=CHAR},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO">
        update juju_character_enhance_card_info
        <set>
                <if test="cardId != null">
                    card_id = #{cardId,jdbcType=VARCHAR},
                </if>
                <if test="startTime != null">
                    start_time = #{startTime,jdbcType=TIMESTAMP},
                </if>
                <if test="startTs != null">
                    start_ts = #{startTs,jdbcType=BIGINT},
                </if>
                <if test="isDeleted != null">
                    is_deleted = #{isDeleted,jdbcType=CHAR},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuCharacterEnhanceCardInfoPO">
        update juju_character_enhance_card_info
        set 
            card_id =  #{cardId,jdbcType=VARCHAR},
            start_time =  #{startTime,jdbcType=TIMESTAMP},
            start_ts =  #{startTs,jdbcType=BIGINT},
            is_deleted =  #{isDeleted,jdbcType=CHAR},
            creator =  #{creator,jdbcType=VARCHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
