<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biligame.activity.community.infrastructure.db.mapper.JujuUserCharacterEnhanceInfoMapper">

    <resultMap id="BaseResultMap" type="com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="server" column="server" jdbcType="VARCHAR"/>
            <result property="cardId" column="card_id" jdbcType="VARCHAR"/>
            <result property="roleId" column="role_id" jdbcType="VARCHAR"/>
            <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
            <result property="cpUid" column="cp_uid" jdbcType="VARCHAR"/>
            <result property="maxPower" column="max_power" jdbcType="INTEGER"/>
            <result property="rank" column="rank" jdbcType="INTEGER"/>
            <result property="globalRank" column="global_rank" jdbcType="INTEGER"/>
            <result property="ds" column="ds" jdbcType="CHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,server,card_id,
        role_id,role_name,cp_uid,
        max_power,rank,global_rank,
        ds,is_deleted,ctime,
        mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from juju_user_character_enhance_info
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from juju_user_character_enhance_info
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO" useGeneratedKeys="true">
        insert into juju_user_character_enhance_info
        ( id,server,card_id
        ,role_id,role_name,cp_uid
        ,max_power,rank,global_rank
        ,ds,is_deleted,ctime
        ,mtime)
        values (#{id,jdbcType=BIGINT},#{server,jdbcType=VARCHAR},#{cardId,jdbcType=VARCHAR}
        ,#{roleId,jdbcType=VARCHAR},#{roleName,jdbcType=VARCHAR},#{cpUid,jdbcType=VARCHAR}
        ,#{maxPower,jdbcType=INTEGER},#{rank,jdbcType=INTEGER},#{globalRank,jdbcType=INTEGER}
        ,#{ds,jdbcType=CHAR},#{isDeleted,jdbcType=CHAR},#{ctime,jdbcType=TIMESTAMP}
        ,#{mtime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO" useGeneratedKeys="true">
        insert into juju_user_character_enhance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="server != null">server,</if>
                <if test="cardId != null">card_id,</if>
                <if test="roleId != null">role_id,</if>
                <if test="roleName != null">role_name,</if>
                <if test="cpUid != null">cp_uid,</if>
                <if test="maxPower != null">max_power,</if>
                <if test="rank != null">rank,</if>
                <if test="globalRank != null">global_rank,</if>
                <if test="ds != null">ds,</if>
                <if test="isDeleted != null">is_deleted,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="server != null">#{server,jdbcType=VARCHAR},</if>
                <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
                <if test="roleId != null">#{roleId,jdbcType=VARCHAR},</if>
                <if test="roleName != null">#{roleName,jdbcType=VARCHAR},</if>
                <if test="cpUid != null">#{cpUid,jdbcType=VARCHAR},</if>
                <if test="maxPower != null">#{maxPower,jdbcType=INTEGER},</if>
                <if test="rank != null">#{rank,jdbcType=INTEGER},</if>
                <if test="globalRank != null">#{globalRank,jdbcType=INTEGER},</if>
                <if test="ds != null">#{ds,jdbcType=CHAR},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=CHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO">
        update juju_user_character_enhance_info
        <set>
                <if test="server != null">
                    server = #{server,jdbcType=VARCHAR},
                </if>
                <if test="cardId != null">
                    card_id = #{cardId,jdbcType=VARCHAR},
                </if>
                <if test="roleId != null">
                    role_id = #{roleId,jdbcType=VARCHAR},
                </if>
                <if test="roleName != null">
                    role_name = #{roleName,jdbcType=VARCHAR},
                </if>
                <if test="cpUid != null">
                    cp_uid = #{cpUid,jdbcType=VARCHAR},
                </if>
                <if test="maxPower != null">
                    max_power = #{maxPower,jdbcType=INTEGER},
                </if>
                <if test="rank != null">
                    rank = #{rank,jdbcType=INTEGER},
                </if>
                <if test="globalRank != null">
                    global_rank = #{globalRank,jdbcType=INTEGER},
                </if>
                <if test="ds != null">
                    ds = #{ds,jdbcType=CHAR},
                </if>
                <if test="isDeleted != null">
                    is_deleted = #{isDeleted,jdbcType=CHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.biligame.activity.community.infrastructure.db.po.JujuUserCharacterEnhanceInfoPO">
        update juju_user_character_enhance_info
        set 
            server =  #{server,jdbcType=VARCHAR},
            card_id =  #{cardId,jdbcType=VARCHAR},
            role_id =  #{roleId,jdbcType=VARCHAR},
            role_name =  #{roleName,jdbcType=VARCHAR},
            cp_uid =  #{cpUid,jdbcType=VARCHAR},
            max_power =  #{maxPower,jdbcType=INTEGER},
            rank =  #{rank,jdbcType=INTEGER},
            global_rank =  #{globalRank,jdbcType=INTEGER},
            ds =  #{ds,jdbcType=CHAR},
            is_deleted =  #{isDeleted,jdbcType=CHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
