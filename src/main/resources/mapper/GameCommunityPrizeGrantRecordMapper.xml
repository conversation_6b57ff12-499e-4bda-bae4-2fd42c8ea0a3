<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biligame.activity.community.infrastructure.db.mapper.GameCommunityPrizeGrantRecordMapper">

    <resultMap id="BaseResultMap" type="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="prizeId" column="prize_id" jdbcType="VARCHAR"/>
            <result property="prizeType" column="prize_type" jdbcType="VARCHAR"/>
            <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
            <result property="bizNo" column="biz_no" jdbcType="VARCHAR"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="serverId" column="server_id" jdbcType="VARCHAR"/>
            <result property="roleId" column="role_id" jdbcType="VARCHAR"/>
            <result property="gameBaseId" column="game_base_id" jdbcType="VARCHAR"/>
            <result property="gameId" column="game_id" jdbcType="VARCHAR"/>
            <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
            <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,prize_id,prize_type,
        biz_type,biz_no,uid,
        server_id,role_id,game_base_id,
        game_id,ext_info,is_deleted,
        ctime,mtime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from game_community_prize_grant_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from game_community_prize_grant_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO" useGeneratedKeys="true">
        insert into game_community_prize_grant_record
        ( id,prize_id,prize_type
        ,biz_type,biz_no,uid
        ,server_id,role_id,game_base_id
        ,game_id,ext_info,is_deleted
        ,ctime,mtime)
        values (#{id,jdbcType=BIGINT},#{prizeId,jdbcType=VARCHAR},#{prizeType,jdbcType=VARCHAR}
        ,#{bizType,jdbcType=VARCHAR},#{bizNo,jdbcType=VARCHAR},#{uid,jdbcType=VARCHAR}
        ,#{serverId,jdbcType=VARCHAR},#{roleId,jdbcType=VARCHAR},#{gameBaseId,jdbcType=VARCHAR}
        ,#{gameId,jdbcType=VARCHAR},#{extInfo,jdbcType=VARCHAR},#{isDeleted,jdbcType=CHAR}
        ,#{ctime,jdbcType=TIMESTAMP},#{mtime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO" useGeneratedKeys="true">
        insert into game_community_prize_grant_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="prizeId != null">prize_id,</if>
                <if test="prizeType != null">prize_type,</if>
                <if test="bizType != null">biz_type,</if>
                <if test="bizNo != null">biz_no,</if>
                <if test="uid != null">uid,</if>
                <if test="serverId != null">server_id,</if>
                <if test="roleId != null">role_id,</if>
                <if test="gameBaseId != null">game_base_id,</if>
                <if test="gameId != null">game_id,</if>
                <if test="extInfo != null">ext_info,</if>
                <if test="isDeleted != null">is_deleted,</if>
                <if test="ctime != null">ctime,</if>
                <if test="mtime != null">mtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="prizeId != null">#{prizeId,jdbcType=VARCHAR},</if>
                <if test="prizeType != null">#{prizeType,jdbcType=VARCHAR},</if>
                <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
                <if test="bizNo != null">#{bizNo,jdbcType=VARCHAR},</if>
                <if test="uid != null">#{uid,jdbcType=VARCHAR},</if>
                <if test="serverId != null">#{serverId,jdbcType=VARCHAR},</if>
                <if test="roleId != null">#{roleId,jdbcType=VARCHAR},</if>
                <if test="gameBaseId != null">#{gameBaseId,jdbcType=VARCHAR},</if>
                <if test="gameId != null">#{gameId,jdbcType=VARCHAR},</if>
                <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
                <if test="isDeleted != null">#{isDeleted,jdbcType=CHAR},</if>
                <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
                <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO">
        update game_community_prize_grant_record
        <set>
                <if test="prizeId != null">
                    prize_id = #{prizeId,jdbcType=VARCHAR},
                </if>
                <if test="prizeType != null">
                    prize_type = #{prizeType,jdbcType=VARCHAR},
                </if>
                <if test="bizType != null">
                    biz_type = #{bizType,jdbcType=VARCHAR},
                </if>
                <if test="bizNo != null">
                    biz_no = #{bizNo,jdbcType=VARCHAR},
                </if>
                <if test="uid != null">
                    uid = #{uid,jdbcType=VARCHAR},
                </if>
                <if test="serverId != null">
                    server_id = #{serverId,jdbcType=VARCHAR},
                </if>
                <if test="roleId != null">
                    role_id = #{roleId,jdbcType=VARCHAR},
                </if>
                <if test="gameBaseId != null">
                    game_base_id = #{gameBaseId,jdbcType=VARCHAR},
                </if>
                <if test="gameId != null">
                    game_id = #{gameId,jdbcType=VARCHAR},
                </if>
                <if test="extInfo != null">
                    ext_info = #{extInfo,jdbcType=VARCHAR},
                </if>
                <if test="isDeleted != null">
                    is_deleted = #{isDeleted,jdbcType=CHAR},
                </if>
                <if test="ctime != null">
                    ctime = #{ctime,jdbcType=TIMESTAMP},
                </if>
                <if test="mtime != null">
                    mtime = #{mtime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.biligame.activity.community.infrastructure.db.po.GameCommunityPrizeGrantRecordPO">
        update game_community_prize_grant_record
        set 
            prize_id =  #{prizeId,jdbcType=VARCHAR},
            prize_type =  #{prizeType,jdbcType=VARCHAR},
            biz_type =  #{bizType,jdbcType=VARCHAR},
            biz_no =  #{bizNo,jdbcType=VARCHAR},
            uid =  #{uid,jdbcType=VARCHAR},
            server_id =  #{serverId,jdbcType=VARCHAR},
            role_id =  #{roleId,jdbcType=VARCHAR},
            game_base_id =  #{gameBaseId,jdbcType=VARCHAR},
            game_id =  #{gameId,jdbcType=VARCHAR},
            ext_info =  #{extInfo,jdbcType=VARCHAR},
            is_deleted =  #{isDeleted,jdbcType=CHAR},
            ctime =  #{ctime,jdbcType=TIMESTAMP},
            mtime =  #{mtime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
