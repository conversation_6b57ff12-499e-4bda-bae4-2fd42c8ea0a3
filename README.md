# Game Activity Community

海外社区游戏活动服务 - 为海外游戏提供成就系统、角色强化、活动管理等社区功能的后端服务

## 项目简介

Game Activity Community 是一个基于 Spring Boot 的微服务应用，主要为海外游戏（特别是咒术回战手游）提供社区相关功能。项目采用领域驱动设计（DDD）架构，支持多区域部署，提供成就查询、角色强化奖励发放等核心功能。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **语言**: Java 11
- **数据库**: MySQL + MyBatis
- **缓存**: Redis + Redisson
- **消息队列**: 支持异步处理
- **构建工具**: Maven
- **其他组件**:
  - JetCache (缓存框架)
  - MapStruct (对象映射)
  - Lombok (代码简化)
  - PageHelper (分页插件)

## 项目架构

项目采用 DDD（领域驱动设计）分层架构：

```
src/main/java/com/biligame/activity/community/
├── application/          # 应用层
│   ├── command/         # 命令服务（写操作）
│   └── query/           # 查询服务（读操作）
├── domain/              # 领域层
│   ├── achievement/     # 成就领域
│   ├── character/       # 角色领域
│   └── guide/          # 指南领域
├── infrastructure/      # 基础设施层
│   ├── config/         # 配置类
│   ├── db/             # 数据库相关
│   ├── rpc/            # 远程调用
│   └── cache/          # 缓存实现
├── shared/             # 共享组件
└── web/                # 表现层
    ├── controller/     # 控制器
    └── vo/            # 视图对象
```

## 核心功能模块

### 1. 成就系统 (Achievement)
- **用户档案查询**: 获取玩家基本信息和成就数据
- **梦幻回楼成就**: 幻境塔相关成就查询和排行榜
- **幻境战成就**: 战斗活动成就统计和排名
- **大讨伐战成就**: 地图活动相关成就数据

**主要接口**:
- `GET /juju/achievement/user_profile` - 用户档案
- `GET /juju/achievement/illusory_tower` - 梦幻回楼成就
- `GET /juju/achievement/battle_event` - 幻境战成就
- `GET /juju/achievement/map_event` - 大讨伐战成就

### 2. 角色强化系统 (Character Enhancement)
- **战力奖励发放**: 基于角色卡片战力等级的奖励分发
- **幂等性保证**: 使用数据库KV存储确保奖励不重复发放
- **角色信息查询**: 获取用户角色强化相关数据

**核心服务**:
- `JuJuCharacterEnhanceCmdService` - 角色强化命令服务
- `JuJuCharacterEnhanceQueryService` - 角色强化查询服务

### 3. 多区域支持
支持以下游戏区域：
- 东南亚 (新加坡)
- 港澳台
- 欧洲 (法兰克福)
- 北美
- 韩国

### 4. 基础设施功能
- **登录验证**: 支持 Token 和 Cookie 两种认证方式
- **限流控制**: 基于注解的接口限流
- **异常处理**: 统一异常处理和错误码管理
- **幂等性服务**: 通用的幂等性实现机制
- **多语言支持**: 支持多种语言的本地化

## 快速开始

### 环境要求
- Java 11+
- Maven 3.6+
- MySQL 5.7+
- Redis 6.0+

### 本地开发

1. **克隆项目**
```bash
git clone https://git.bilibili.co/game/server/bussiness/game-activity-community.git
cd game-activity-community
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE activity_community;

# 执行初始化脚本
mysql -u root -p activity_community < src/main/resources/sql/idempotent_kv.sql
```

3. **配置 Redis**
```bash
# 启动 Redis 服务
redis-server
```

4. **修改配置文件**
```yaml
# src/main/resources/application.yml
mysql:
  url: ***************************************************************************************************
  username: your_username
  password: your_password

redis:
  redisson:
    address: redis://localhost:6379
    password: your_redis_password
```

5. **启动应用**
```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8083` 启动

### Docker 部署

```bash
# 构建镜像
mvn clean package
docker build -t game-activity-community .

# 运行容器
docker run -p 8083:8083 game-activity-community
```

## API 文档

### 认证方式
所有接口都需要登录认证，支持以下方式：
- **Token 认证**: 在请求头中添加 `Authorization: Bearer <token>`
- **Cookie 认证**: 使用登录后的 Cookie

### 主要接口

#### 成就相关
```http
GET /juju/achievement/user_profile?game_id=1&app_id=11126907&lang=zh-cn
GET /juju/achievement/illusory_tower?game_id=1&app_id=11126907&lang=zh-cn
GET /juju/achievement/battle_event?game_id=1&app_id=11126907&lang=zh-cn
GET /juju/achievement/map_event?game_id=1&app_id=11126907&lang=zh-cn
```

#### 角色强化相关
```http
POST /juju/character/enhance/reward
{
  "roleId": 123456,
  "cardId": 789,
  "rewardId": 101
}
```

## 配置说明

### 数据库配置
```yaml
mysql:
  cluster: shylf_game_game_activity_mng_cluster  # 生产环境集群
  url: *******************************
  username: username
  password: password
```

### Redis 配置
```yaml
redis:
  redisson:
    cluster: shylf_game_game_activity_mng_cluster
    address: redis://host:port
    database: 0
    timeout: 3000
    connection-pool-size: 64
```

### 外部服务配置
```yaml
http:
  client:
    game-dip-api:          # 游戏数据接口
      host: discovery://game.dip.game-dip-api
    overseas-center-login:  # 海外登录中心
      host: discovery://game.overseas-login.overseas-center-login
    juju-cos:              # 咒术回战资源服务
      host: https://l16-dev-patch-ghost-sgp.bilibiligame.net
```

## 开发指南

### 代码规范
- 遵循 DDD 分层架构原则
- 使用 MapStruct 进行对象转换
- 统一使用 Lombok 简化代码
- 接口需要添加适当的限流和认证注解

### 新增功能
1. 在对应的 domain 包下创建领域模型
2. 在 application 层实现业务逻辑
3. 在 infrastructure 层实现技术细节
4. 在 web 层暴露 REST 接口

### 数据库操作
- 使用 MyBatis 进行数据库操作
- 所有表都包含标准字段：id, ctime, mtime
- 幂等性表不需要 expire_time 字段

## 监控和日志

### 日志配置
项目使用 SLF4J + Logback 进行日志管理：
- 请求日志：记录所有 HTTP 请求
- 业务日志：记录关键业务操作
- 错误日志：记录异常和错误信息

### 性能监控
- 使用 JaCoCo 进行代码覆盖率统计
- 支持 APM 监控集成
- Redis 和数据库连接池监控

## 部署环境

### 开发环境
- 数据库：*************:3307
- Redis：本地或开发环境 Redis

### 生产环境
- 使用服务发现机制
- 集群化部署
- 负载均衡

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目为内部项目，版权归 Bilibili 游戏所有。

## 联系方式

- 项目负责人：haojipeng
- 邮箱：<EMAIL>
- 项目地址：https://git.bilibili.co/game/server/bussiness/game-activity-community
