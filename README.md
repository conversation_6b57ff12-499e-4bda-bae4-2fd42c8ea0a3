# Game Activity Community

## Overview

The `game-activity-community` is a Spring Boot microservice designed to provide comprehensive community features for overseas games, particularly focusing on the JujutsuKaisen mobile game. Built with Domain-Driven Design (DDD) architecture, it supports multi-region deployment and offers core functionalities including achievement queries, character enhancement reward distribution, and activity management.

## Architecture

The service follows a Domain-Driven Design (DDD) layered architecture, ensuring clean separation of concerns and facilitating easy maintenance and scalability.

```
src/main/java/com/biligame/activity/community/
├── application/          # Application Layer
│   ├── command/         # Command Services (Write Operations)
│   └── query/           # Query Services (Read Operations)
├── domain/              # Domain Layer
│   ├── achievement/     # Achievement Domain
│   ├── character/       # Character Domain
│   └── guide/          # Guide Domain
├── infrastructure/      # Infrastructure Layer
│   ├── config/         # Configuration Classes
│   ├── db/             # Database Components
│   ├── rpc/            # Remote Procedure Calls
│   └── cache/          # Cache Implementation
├── shared/             # Shared Components
└── web/                # Presentation Layer
    ├── controller/     # REST Controllers
    └── vo/            # View Objects
```

## Modules

- **game-activity-community-application**: Implements the core application logic, orchestrating achievement queries and character enhancement services.
- **game-activity-community-domain**: Provides the domain model and business logic for achievement tracking, character progression, and multi-region game support.
- **game-activity-community-infrastructure**: Manages infrastructure-related components including database operations, caching, RPC calls, and external service integrations.
- **game-activity-community-web**: Offers RESTful API endpoints for seamless interaction with the game community service.
